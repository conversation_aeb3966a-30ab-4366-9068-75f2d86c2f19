{"name": "@passa/backend", "version": "1.0.0", "description": "Passa Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run", "db:setup": "ts-node src/scripts/setup-database.ts", "db:status": "knex migrate:status", "db:validate": "ts-node src/scripts/validate-schema.ts", "docs:generate": "typedoc src/index.ts"}, "dependencies": {"aws-sdk": "^2.1518.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.12.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "qrcode": "^1.5.3", "redis": "^4.6.12", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "stellar-sdk": "^11.2.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/bull": "^4.10.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.9", "@types/qrcode": "^1.5.5", "@types/redis": "^4.0.11", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "typedoc": "^0.25.4", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}