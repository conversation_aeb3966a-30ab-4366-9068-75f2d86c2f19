import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { Request, Response } from 'express';
import { UserController } from '@/controllers/UserController';
import { AuthenticatedRequest } from '@/middleware/auth';

// Mock the models
jest.mock('@/models/User', () => ({
  UserModel: {
    findById: jest.fn(),
    update: jest.fn(),
    search: jest.fn(),
    updateStatus: jest.fn(),
    delete: jest.fn(),
  },
  UserValidationError: class extends Error {
    constructor(message: string, public field: string) {
      super(message);
    }
  },
}));

jest.mock('@/models/UserProfile', () => ({
  UserProfileModel: {
    findByUserId: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  UserProfileValidationError: class extends Error {
    constructor(message: string, public field: string) {
      super(message);
    }
  },
}));

jest.mock('@/models/UserActivity', () => ({
  UserActivityModel: {
    logActivity: jest.fn(),
    getActivityStats: jest.fn(),
    getActivityHistory: jest.fn(),
    getActivityCount: jest.fn(),
  },
}));

jest.mock('@/models/UserFollow', () => ({
  UserFollowModel: {
    getFollowCounts: jest.fn(),
    followUser: jest.fn(),
    unfollowUser: jest.fn(),
    getFollowers: jest.fn(),
    getFollowing: jest.fn(),
    isFollowing: jest.fn(),
  },
}));

describe('UserController Integration Tests', () => {
  let mockRequest: Partial<AuthenticatedRequest>;
  let mockResponse: Partial<Response>;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;

  beforeEach(() => {
    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    
    mockRequest = {
      user: { id: 1, email: '<EMAIL>', role: 'user' },
      body: {},
      params: {},
      query: {},
    };
    
    mockResponse = {
      json: mockJson as any,
      status: mockStatus as any,
    };

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should return user profile successfully', async () => {
      const { UserModel } = require('@/models/User');
      const { UserProfileModel } = require('@/models/UserProfile');
      const { UserFollowModel } = require('@/models/UserFollow');

      // Mock successful responses
      UserModel.findById.mockResolvedValue({
        user_id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        status: 'active',
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date(),
      });

      UserProfileModel.findByUserId.mockResolvedValue({
        profile_id: 1,
        user_id: 1,
        bio: 'Test bio',
        avatar_url: 'https://example.com/avatar.jpg',
      });

      UserFollowModel.getFollowCounts.mockResolvedValue({
        followers: 10,
        following: 5,
      });

      await UserController.getProfile(mockRequest as Request, mockResponse as Response);

      expect(mockStatus).not.toHaveBeenCalled();
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          user: expect.objectContaining({
            user_id: 1,
            username: 'testuser',
            email: '<EMAIL>',
          }),
          profile: expect.objectContaining({
            bio: 'Test bio',
          }),
          follow_counts: expect.objectContaining({
            followers: 10,
            following: 5,
          }),
        })
      );
    });

    it('should return 404 when user not found', async () => {
      const { UserModel } = require('@/models/User');
      UserModel.findById.mockResolvedValue(null);

      await UserController.getProfile(mockRequest as Request, mockResponse as Response);

      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({ error: 'User not found' });
    });
  });

  describe('getDashboard', () => {
    it('should return dashboard data successfully', async () => {
      const { UserModel } = require('@/models/User');
      const { UserProfileModel } = require('@/models/UserProfile');
      const { UserActivityModel } = require('@/models/UserActivity');
      const { UserFollowModel } = require('@/models/UserFollow');

      // Mock successful responses
      UserModel.findById.mockResolvedValue({
        user_id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        created_at: new Date(),
      });

      UserProfileModel.findByUserId.mockResolvedValue({
        profile_completion_percentage: 75,
        bio: 'Test bio',
      });

      UserActivityModel.getActivityStats.mockResolvedValue({
        total_actions: 100,
        last_activity: new Date(),
      });

      UserActivityModel.getActivityHistory.mockResolvedValue([
        { action: 'login', created_at: new Date() },
      ]);

      UserFollowModel.getFollowCounts.mockResolvedValue({
        followers: 10,
        following: 5,
      });

      await UserController.getDashboard(mockRequest as Request, mockResponse as Response);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          user: expect.objectContaining({
            user_id: 1,
            username: 'testuser',
          }),
          profile: expect.objectContaining({
            profile_completion: 75,
          }),
          activity: expect.objectContaining({
            stats: expect.any(Object),
            recent: expect.any(Array),
          }),
          follow_counts: expect.objectContaining({
            followers: 10,
            following: 5,
          }),
        })
      );
    });
  });

  describe('updateSettings', () => {
    it('should update user settings successfully', async () => {
      const { UserProfileModel } = require('@/models/UserProfile');

      mockRequest.body = {
        preferences: { theme: 'dark' },
        notification_settings: { email: true },
      };

      UserProfileModel.findByUserId.mockResolvedValue({
        profile_id: 1,
        user_id: 1,
      });

      UserProfileModel.update.mockResolvedValue({
        profile_id: 1,
        user_id: 1,
        preferences: { theme: 'dark' },
        notification_settings: { email: true },
      });

      await UserController.updateSettings(mockRequest as Request, mockResponse as Response);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Settings updated successfully',
          preferences: { theme: 'dark' },
          notification_settings: { email: true },
        })
      );
    });
  });

  describe('followUser', () => {
    it('should follow user successfully', async () => {
      const { UserModel } = require('@/models/User');
      const { UserFollowModel } = require('@/models/UserFollow');

      mockRequest.params = { userId: '2' };

      UserModel.findById.mockResolvedValue({
        user_id: 2,
        username: 'targetuser',
      });

      UserFollowModel.followUser.mockResolvedValue({
        follow_id: 1,
        follower_id: 1,
        following_id: 2,
        created_at: new Date(),
      });

      await UserController.followUser(mockRequest as Request, mockResponse as Response);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Successfully followed user',
          follow: expect.objectContaining({
            follower_id: 1,
            following_id: 2,
          }),
        })
      );
    });

    it('should prevent following yourself', async () => {
      mockRequest.params = { userId: '1' }; // Same as authenticated user ID

      await UserController.followUser(mockRequest as Request, mockResponse as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Cannot follow yourself' });
    });
  });

  describe('searchUsers', () => {
    it('should search users successfully', async () => {
      const { UserModel } = require('@/models/User');

      mockRequest.query = {
        username: 'test',
        page: '1',
        limit: '10',
      };

      UserModel.search.mockResolvedValue({
        data: [
          {
            user_id: 2,
            username: 'testuser2',
            first_name: 'Test',
            last_name: 'User2',
            created_at: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      });

      await UserController.searchUsers(mockRequest as Request, mockResponse as Response);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.arrayContaining([
            expect.objectContaining({
              user_id: 2,
              username: 'testuser2',
            }),
          ]),
          total: 1,
          page: 1,
          limit: 10,
        })
      );
    });
  });
});
