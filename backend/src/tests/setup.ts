// Test setup file

// Set test environment
process.env['NODE_ENV'] = 'test';
process.env['DATABASE_NAME'] = 'passa_dev_test';

// Increase timeout for database operations
jest.setTimeout(30000);

// Mock database for tests that don't need real DB
jest.mock('@/config/database', () => ({
  db: {
    migrate: {
      latest: jest.fn().mockResolvedValue(undefined),
    },
    destroy: jest.fn().mockResolvedValue(undefined),
    fn: {
      now: jest.fn().mockReturnValue(new Date()),
    },
    raw: jest.fn().mockResolvedValue({ rows: [] }),
  },
  testConnection: jest.fn().mockResolvedValue(true),
  closeConnection: jest.fn().mockResolvedValue(undefined),
}));

// Global test setup
beforeAll(async () => {
  // Any global setup can go here
});

afterAll(async () => {
  // Any global cleanup can go here
});
