#!/usr/bin/env node

/**
 * Comprehensive test script for User Management functionality
 * This script tests all the implemented user controller endpoints
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

const BASE_URL = 'http://localhost:3000';
const JWT_SECRET = 'your_super_secret_jwt_key_here'; // From .env

// Test user data with unique timestamp
const timestamp = Date.now();
const testUser1 = {
  username: `testuser1_${timestamp}`,
  email: `test1_${timestamp}@example.com`,
  password: 'password123',
  first_name: 'Test',
  last_name: 'User'
};

const testUser2 = {
  username: 'testuser2',
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'Test',
  last_name: 'User'
};

// Generate test JWT token
function generateTestToken(userId, email, role = 'user') {
  return jwt.sign(
    { id: userId, email, role },
    JWT_SECRET,
    { expiresIn: '1h' }
  );
}

// Helper function to make authenticated requests
function makeAuthRequest(method, url, data = null, token) {
  const config = {
    method,
    url: `${BASE_URL}${url}`,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    config.data = data;
  }
  
  return axios(config);
}

// Test functions
async function testUserRegistration() {
  console.log('\n🧪 Testing User Registration...');

  try {
    const response = await axios.post(`${BASE_URL}/auth/signup`, testUser1);
    console.log('✅ User registration successful');
    console.log('   Response:', response.data.message);
    return response.data.user;
  } catch (error) {
    console.log('❌ User registration failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testUserLogin() {
  console.log('\n🧪 Testing User Login...');

  try {
    const response = await axios.post(`${BASE_URL}/auth/signin`, {
      email: testUser1.email,
      password: testUser1.password
    });
    console.log('✅ User login successful');
    console.log('   Token received:', response.data.token ? 'Yes' : 'No');
    return response.data.token;
  } catch (error) {
    console.log('❌ User login failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testGetProfile(token) {
  console.log('\n🧪 Testing Get User Profile...');
  
  try {
    const response = await makeAuthRequest('GET', '/api/users/profile', null, token);
    console.log('✅ Get profile successful');
    console.log('   User ID:', response.data.user?.user_id);
    console.log('   Username:', response.data.user?.username);
    console.log('   Profile completion:', response.data.profile?.profile_completion || 0, '%');
    return response.data;
  } catch (error) {
    console.log('❌ Get profile failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testUpdateProfile(token) {
  console.log('\n🧪 Testing Update User Profile...');
  
  try {
    const updateData = {
      first_name: 'Updated',
      last_name: 'Name',
      phone: '+1234567890'
    };
    
    const response = await makeAuthRequest('PUT', '/api/users/profile', updateData, token);
    console.log('✅ Update profile successful');
    console.log('   Updated name:', response.data.user?.first_name, response.data.user?.last_name);
    return response.data;
  } catch (error) {
    console.log('❌ Update profile failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testUpdateProfileDetails(token) {
  console.log('\n🧪 Testing Update Profile Details...');
  
  try {
    const profileData = {
      bio: 'This is my test bio',
      location: 'Test City',
      social_links: {
        website_url: 'https://example.com',
        twitter_handle: 'testuser'
      }
    };
    
    const response = await makeAuthRequest('PUT', '/api/users/profile/details', profileData, token);
    console.log('✅ Update profile details successful');
    console.log('   Bio:', response.data.profile?.bio);
    console.log('   Location:', response.data.profile?.location);
    return response.data;
  } catch (error) {
    console.log('❌ Update profile details failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testGetDashboard(token) {
  console.log('\n🧪 Testing Get Dashboard...');
  
  try {
    const response = await makeAuthRequest('GET', '/api/users/dashboard', null, token);
    console.log('✅ Get dashboard successful');
    console.log('   Profile completion:', response.data.profile?.profile_completion || 0, '%');
    console.log('   Followers:', response.data.follow_counts?.followers || 0);
    console.log('   Following:', response.data.follow_counts?.following || 0);
    return response.data;
  } catch (error) {
    console.log('❌ Get dashboard failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testUpdateSettings(token) {
  console.log('\n🧪 Testing Update User Settings...');
  
  try {
    const settingsData = {
      preferences: {
        theme: 'dark',
        language: 'en',
        timezone: 'UTC'
      },
      notification_settings: {
        email: true,
        push: false,
        sms: false
      }
    };
    
    const response = await makeAuthRequest('PUT', '/api/users/settings', settingsData, token);
    console.log('✅ Update settings successful');
    console.log('   Theme preference:', response.data.preferences?.theme);
    return response.data;
  } catch (error) {
    console.log('❌ Update settings failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testSearchUsers(token) {
  console.log('\n🧪 Testing Search Users...');
  
  try {
    const response = await makeAuthRequest('GET', '/api/users/search?username=test&limit=5', null, token);
    console.log('✅ Search users successful');
    console.log('   Found users:', response.data.data?.length || 0);
    console.log('   Total results:', response.data.total || 0);
    return response.data;
  } catch (error) {
    console.log('❌ Search users failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testGetUserStats(token) {
  console.log('\n🧪 Testing Get User Statistics...');
  
  try {
    const response = await makeAuthRequest('GET', '/api/users/stats', null, token);
    console.log('✅ Get user stats successful');
    console.log('   Account age (days):', response.data.account_age_days || 0);
    console.log('   Total activities:', response.data.total_activities || 0);
    return response.data;
  } catch (error) {
    console.log('❌ Get user stats failed:', error.response?.data?.error || error.message);
    return null;
  }
}

async function testGetActivityHistory(token) {
  console.log('\n🧪 Testing Get Activity History...');
  
  try {
    const response = await makeAuthRequest('GET', '/api/users/activity?limit=5', null, token);
    console.log('✅ Get activity history successful');
    console.log('   Activities found:', response.data.data?.length || 0);
    return response.data;
  } catch (error) {
    console.log('❌ Get activity history failed:', error.response?.data?.error || error.message);
    return null;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting User Management Functionality Tests');
  console.log('================================================');
  
  // Test user registration and login
  const user = await testUserRegistration();
  if (!user) {
    console.log('\n❌ Cannot continue tests without user registration');
    return;
  }
  
  const token = await testUserLogin();
  if (!token) {
    console.log('\n❌ Cannot continue tests without authentication token');
    return;
  }
  
  // Test all user management features
  await testGetProfile(token);
  await testUpdateProfile(token);
  await testUpdateProfileDetails(token);
  await testGetDashboard(token);
  await testUpdateSettings(token);
  await testSearchUsers(token);
  await testGetUserStats(token);
  await testGetActivityHistory(token);
  
  console.log('\n🎉 All User Management Tests Completed!');
  console.log('================================================');
}

// Check if server is running
async function checkServerHealth() {
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Server is running');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first.');
    console.log('   Run: npm run dev');
    return false;
  }
}

// Run tests
async function main() {
  const serverRunning = await checkServerHealth();
  if (serverRunning) {
    await runAllTests();
  }
}

main().catch(console.error);
